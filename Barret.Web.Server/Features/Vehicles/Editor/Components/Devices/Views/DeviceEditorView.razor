@using Barret.Core.Areas.Devices.Enums
@using Barret.Shared.DTOs.Devices
@using Barret.Web.Server.Shared.Components.DeviceEditors.Tabs
@using Barret.Web.Server.Shared.Components.DeviceManagers
@using Barret.Web.Server.Features.Shared.Components.BarretDevExpress
@using DevExpress.Blazor
@inherits DeviceEditorViewBase

<DxPopup @bind-Visible="@ViewModel.IsVisible"
         HeaderText="@($"{(ViewModel.IsAdding ? "Add" : "Edit")} {ViewModel.Device.DeviceRole}")"
         ShowFooter="true"
         Width="900px"
         CloseOnEscape="true"
         CloseOnOutsideClick="false"
         ShowCloseButton="true"
         CssClass="device-editor-popup accessible-popup"
         TabIndex="0">
    <HeaderTemplate>
        <div class="d-flex align-items-center">
            <i class="bi bi-device-ssd me-2"></i>
            <span>@($"{(ViewModel.IsAdding ? "Add" : "Edit")} {ViewModel.Device.DeviceRole}")</span>
        </div>
    </HeaderTemplate>
    <ChildContent>
        <div class="device-editor-container">
            <DxTabs CssClass="device-editor-tabs"
                   Orientation="TabOrientation.Vertical"
                   ActiveTabIndex="@ViewModel.ActiveTabIndex"
                   ActiveTabIndexChanged="@((index) => ViewModel.ActiveTabIndex = index)">

                @foreach (var tab in TabService.GetTabsForDevice(ViewModel.Device))
                {
                    <DxTabPage TabIconCssClass="@GetIconClassForTab(tab.Name)" Text="@tab.Name">
                        <ChildContent>
                            <div class="tab-content-panel">
                                @if (tab.Name == "General")
                                {
                                    <BasicInfoTab Device="@ViewModel.Device" OnPropertyChanged="@OnTabPropertyChanged" />
                                }
                                else if (tab.Name == "Make/Model")
                                {
                                    <ModelTab
                                        Device="@ViewModel.Device"
                                        Manufacturers="@ViewModel.Manufacturers"
                                        IsModelRequired="false"
                                        ShowValidationErrors="@(!ViewModel.IsDeviceValid)"
                                        OnManufacturerSelected="@OnManufacturerChangedAsync"
                                        OnPropertyChanged="@OnTabPropertyChanged" />
                                }
                                else if (tab.Name == "Position")
                                {
                                    @if (tab.ComponentType == typeof(MaritimePositionTab))
                                    {
                                        <MaritimePositionTab Device="@ViewModel.Device" OnPropertyChanged="@OnTabPropertyChanged" />
                                    }
                                    else
                                    {
                                        <PositionTab Device="@ViewModel.Device" OnPropertyChanged="@OnTabPropertyChanged" />
                                    }
                                }
                                @* else if (tab.Name == "Connection")
                                {
                                    <ConnectionTab Device="@ViewModel.Device" RequireConnection="false" />
                                } *@
                                else if (tab.Name == "Settings")
                                {
                                    @if (tab.ComponentType == typeof(CameraTab))
                                    {
                                        <CameraTab Device="@ViewModel.Device" OnPropertyChanged="@OnTabPropertyChanged" />
                                    }
                                    else if (tab.ComponentType == typeof(EngineTab))
                                    {
                                        <EngineTab Device="@ViewModel.Device" OnPropertyChanged="@OnTabPropertyChanged" />
                                    }
                                    else if (tab.ComponentType == typeof(ThrusterTab))
                                    {
                                        <ThrusterTab Device="@ViewModel.Device" OnPropertyChanged="@OnTabPropertyChanged" />
                                    }
                                    else if (tab.ComponentType == typeof(RadarTab))
                                    {
                                        <RadarTab Device="@ViewModel.Device" OnPropertyChanged="@OnTabPropertyChanged" />
                                    }
                                    else if (tab.ComponentType == typeof(LightTab))
                                    {
                                        <LightTab Device="@ViewModel.Device" OnPropertyChanged="@OnTabPropertyChanged" />
                                    }
                                    else if (tab.ComponentType == typeof(RudderTab))
                                    {
                                        <RudderTab Device="@ViewModel.Device" OnPropertyChanged="@OnTabPropertyChanged" />
                                    }
                                    else if (tab.ComponentType == typeof(HornTab))
                                    {
                                        <HornTab Device="@ViewModel.Device" OnPropertyChanged="@OnTabPropertyChanged" />
                                    }
                                    else if (tab.ComponentType == typeof(AntennaTab))
                                    {
                                        <AntennaTab Device="@ViewModel.Device" OnPropertyChanged="@OnTabPropertyChanged" />
                                    }
                                    else if (tab.ComponentType == typeof(AutopilotTab))
                                    {
                                        <AutopilotTab Device="@ViewModel.Device" OnPropertyChanged="@OnTabPropertyChanged" />
                                    }
                                }
                                else if (tab.Name == "Alarms")
                                {
                                    <AlarmManagerTab Device="@ViewModel.Device" OnDeviceChanged="@OnDeviceChanged" />
                                }
                            </div>
                        </ChildContent>
                    </DxTabPage>
                }

                @* <!-- Connections Tab - Shown for all devices -->
                @if (!IsAddingInterface)
                {
                    <DxTabPage TabIconCssClass="bi bi-diagram-3" Text="Connections">
                        <ChildContent>
                            <div class="tab-content-panel">
                                <h5>Device Connections</h5>
                                <p class="text-muted">
                                    Connections allow this device to connect to other devices.
                                    You can add connections after saving this device.
                                </p>
                            </div>
                        </ChildContent>
                    </DxTabPage>
                } *@
            </DxTabs>
        </div>
    </ChildContent>
    <FooterTemplate>
        <div class="p-3 d-flex justify-content-between align-items-center">
            <div>
                @if (!ViewModel.IsDeviceValid)
                {
                    <div class="text-danger">
                        <i class="bi bi-exclamation-triangle-fill me-1"></i>
                        <small>Please fill in all required fields</small>
                    </div>
                }
            </div>
            <div class="d-flex gap-2">
                <DxButton Text="Cancel"
                         RenderStyle="ButtonRenderStyle.Secondary"
                         Click="@CancelEdit" />
                <DxButton Text="Save"
                         RenderStyle="ButtonRenderStyle.Primary"
                         Click="@SaveDeviceAsync"
                         Enabled="@ViewModel.IsDeviceValid">
                    <ChildContent Context="buttonContext">
                        @if (ViewModel.IsSaving)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            <span>Saving...</span>
                        }
                    </ChildContent>
                </DxButton>
            </div>
        </div>
    </FooterTemplate>
</DxPopup>
